# AI 视频编辑器 - 视频实时预览引擎

这是一个基于 Vue 3 + TypeScript + 组合式 API 的视频实时预览引擎，提供了完整的视频编辑界面。

## 功能特性

### 🎬 核心功能
- **视频预览窗口**: 实时播放当前时间轴位置的视频内容
- **时间轴管理**: 可视化的时间轴，支持拖拽视频片段
- **播放控制**: 完整的播放控制面板，包括播放/暂停、跳转、速度控制等
- **拖拽上传**: 支持直接拖拽视频文件到时间轴

### 🎯 主要组件

#### 1. VideoPreviewEngine.vue
主容器组件，整合所有子组件

#### 2. PreviewWindow.vue
- 视频预览窗口
- 支持多种视频格式 (MP4, WebM, AVI 等)
- 实时同步时间轴位置
- 显示当前播放时间

#### 3. Timeline.vue
- 时间轴容器
- 支持拖拽文件上传
- 视频片段管理
- 背景网格显示

#### 4. VideoClip.vue
- 可拖拽的视频片段
- 缩略图预览
- 右键菜单操作
- 片段信息显示

#### 5. TimeScale.vue
- 时间刻度标记
- 播放头指示器
- 点击跳转功能

#### 6. PlaybackControls.vue
- 播放/暂停控制
- 时间跳转按钮
- 进度条拖拽
- 音量控制
- 播放速度调节

## 使用方法

### 1. 启动项目
```bash
cd frontend
npm install
npm run dev
```

### 2. 基本操作

#### 上传视频
1. 将视频文件拖拽到时间轴区域
2. 视频片段会自动添加到拖拽位置
3. 支持多个视频文件同时上传

#### 编辑视频片段
1. **拖拽移动**: 点击并拖拽视频片段到新位置
2. **删除片段**: 右键点击片段选择"删除"
3. **复制片段**: 右键点击片段选择"复制"

#### 播放控制
1. **播放/暂停**: 点击中央播放按钮
2. **时间跳转**: 点击时间轴任意位置
3. **快进/快退**: 使用前进/后退按钮（10秒间隔）
4. **速度控制**: 使用右侧速度选择器

### 3. 技术架构

#### 状态管理 (Pinia)
```typescript
interface VideoClip {
  id: string
  file: File
  url: string
  duration: number
  startTime: number
  endTime: number
  timelinePosition: number
  name: string
}
```

#### 核心功能
- **实时预览**: 根据时间轴位置自动切换视频片段
- **拖拽系统**: 原生 HTML5 拖拽 API
- **时间同步**: 视频播放与时间轴实时同步
- **响应式设计**: 适配不同屏幕尺寸

## 支持的视频格式

- MP4 (推荐)
- WebM
- AVI
- MOV
- 其他浏览器支持的视频格式

## 浏览器兼容性

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 开发计划

### 即将实现的功能
- [ ] 视频片段裁剪
- [ ] 音频轨道支持
- [ ] 转场效果
- [ ] 视频导出
- [ ] 撤销/重做功能
- [ ] 键盘快捷键

### 技术优化
- [ ] 虚拟滚动优化大量片段性能
- [ ] Web Workers 处理视频解析
- [ ] 缓存机制优化加载速度
- [ ] 更精确的时间同步

## 项目结构

```
frontend/src/
├── components/
│   ├── VideoPreviewEngine.vue  # 主容器
│   ├── PreviewWindow.vue       # 预览窗口
│   ├── Timeline.vue            # 时间轴
│   ├── VideoClip.vue          # 视频片段
│   ├── TimeScale.vue          # 时间刻度
│   └── PlaybackControls.vue   # 播放控制
├── stores/
│   └── counter.ts             # 视频状态管理
└── App.vue                    # 应用入口
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
